#include "SystemInfo.h"
#include "Utils.h"
#include <iostream>
#include <sstream>
#include <comdef.h>
#include <wbemidl.h>

// Note: #pragma comment is MSVC specific, MinGW uses -l flags instead

SystemInfo::SystemInfo() 
    : m_hQuery(nullptr)
    , m_hCpuCounter(nullptr)
    , m_hMemoryCounter(nullptr)
    , m_dwLastInOctets(0)
    , m_dwLastOutOctets(0)
    , m_bFirstNetworkRead(true)
    , m_bGpuMonitorAvailable(false)
    , m_systemDrive(L"C:\\") {
    
    m_lastNetworkTime = std::chrono::steady_clock::now();
}

SystemInfo::~SystemInfo() {
    Cleanup();
}

bool SystemInfo::Initialize() {
    // 初始化PDH
    if (!InitializePDH()) {
        return false;
    }
    
    // 初始化网络监控
    InitializeNetworkMonitor();
    
    // 初始化GPU监控
    InitializeGPUMonitor();
    
    // 获取系统盘符
    wchar_t systemPath[MAX_PATH];
    if (GetSystemDirectory(systemPath, MAX_PATH)) {
        m_systemDrive = std::wstring(systemPath, 3); // "C:\"
    }
    
    return true;
}

void SystemInfo::Cleanup() {
    if (m_hCpuCounter) {
        PdhRemoveCounter(m_hCpuCounter);
        m_hCpuCounter = nullptr;
    }
    
    if (m_hMemoryCounter) {
        PdhRemoveCounter(m_hMemoryCounter);
        m_hMemoryCounter = nullptr;
    }
    
    if (m_hQuery) {
        PdhCloseQuery(m_hQuery);
        m_hQuery = nullptr;
    }
}

bool SystemInfo::InitializePDH() {
    PDH_STATUS status = PdhOpenQuery(nullptr, 0, &m_hQuery);
    if (status != ERROR_SUCCESS) {
        return false;
    }
    
    // 添加CPU使用率计数器
    status = PdhAddCounter(m_hQuery, L"\\Processor(_Total)\\% Processor Time", 0, &m_hCpuCounter);
    if (status != ERROR_SUCCESS) {
        return false;
    }
    
    // 添加内存使用率计数器
    status = PdhAddCounter(m_hQuery, L"\\Memory\\Available MBytes", 0, &m_hMemoryCounter);
    if (status != ERROR_SUCCESS) {
        return false;
    }
    
    // 收集初始数据
    PdhCollectQueryData(m_hQuery);
    Sleep(100); // 等待一小段时间以获得有效的CPU数据
    
    return true;
}

bool SystemInfo::InitializeNetworkMonitor() {
    // 获取网络接口信息
    DWORD dwSize = 0;
    DWORD dwRetVal = GetIfTable(nullptr, &dwSize, FALSE);
    
    if (dwRetVal == ERROR_INSUFFICIENT_BUFFER) {
        std::vector<BYTE> buffer(dwSize);
        PMIB_IFTABLE pIfTable = reinterpret_cast<PMIB_IFTABLE>(buffer.data());
        
        dwRetVal = GetIfTable(pIfTable, &dwSize, FALSE);
        if (dwRetVal == NO_ERROR) {
            // 找到第一个活动的网络接口
            for (DWORD i = 0; i < pIfTable->dwNumEntries; i++) {
                if (pIfTable->table[i].dwOperStatus == MIB_IF_OPER_STATUS_OPERATIONAL &&
                    pIfTable->table[i].dwType == MIB_IF_TYPE_ETHERNET) {
                    m_dwLastInOctets = pIfTable->table[i].dwInOctets;
                    m_dwLastOutOctets = pIfTable->table[i].dwOutOctets;
                    break;
                }
            }
            return true;
        }
    }
    
    return false;
}

bool SystemInfo::InitializeGPUMonitor() {
    // 这里可以实现GPU监控的初始化
    // 由于GPU监控比较复杂，暂时设为不可用
    m_bGpuMonitorAvailable = false;
    return false;
}

SystemStatus SystemInfo::GetSystemStatus() {
    SystemStatus status;
    
    status.cpuUsage = GetCPUUsage();
    status.memoryUsage = GetMemoryUsage();
    status.gpuUsage = GetGPUUsage();
    status.diskUsage = GetDiskUsage();
    GetNetworkSpeed(status.networkUpload, status.networkDownload);
    
    status.cpuName = GetCPUName();
    status.gpuName = GetGPUName();
    GetMemoryInfo(status.totalMemory, status.availableMemory);
    
    return status;
}

double SystemInfo::GetCPUUsage() {
    if (!m_hQuery || !m_hCpuCounter) {
        return 0.0;
    }
    
    PDH_STATUS status = PdhCollectQueryData(m_hQuery);
    if (status != ERROR_SUCCESS) {
        return 0.0;
    }
    
    PDH_FMT_COUNTERVALUE counterValue;
    status = PdhGetFormattedCounterValue(m_hCpuCounter, PDH_FMT_DOUBLE, nullptr, &counterValue);
    if (status != ERROR_SUCCESS) {
        return 0.0;
    }
    
    return Utils::Clamp(counterValue.doubleValue, 0.0, 100.0);
}

double SystemInfo::GetMemoryUsage() {
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    
    if (GlobalMemoryStatusEx(&memInfo)) {
        return static_cast<double>(memInfo.dwMemoryLoad);
    }
    
    return 0.0;
}

double SystemInfo::GetGPUUsage() {
    // GPU使用率获取比较复杂，这里返回模拟数据
    // 实际实现需要使用NVIDIA-ML API、AMD ADL或Windows Performance Toolkit
    return 0.0;
}

double SystemInfo::GetDiskUsage() {
    return GetDiskUsageForDrive(m_systemDrive);
}

double SystemInfo::GetDiskUsageForDrive(const std::wstring& drive) {
    ULARGE_INTEGER freeBytesAvailable, totalNumberOfBytes, totalNumberOfFreeBytes;
    
    if (GetDiskFreeSpaceEx(drive.c_str(), &freeBytesAvailable, &totalNumberOfBytes, &totalNumberOfFreeBytes)) {
        if (totalNumberOfBytes.QuadPart > 0) {
            double usedBytes = static_cast<double>(totalNumberOfBytes.QuadPart - totalNumberOfFreeBytes.QuadPart);
            double totalBytes = static_cast<double>(totalNumberOfBytes.QuadPart);
            return (usedBytes / totalBytes) * 100.0;
        }
    }
    
    return 0.0;
}

void SystemInfo::GetNetworkSpeed(double& uploadSpeed, double& downloadSpeed) {
    uploadSpeed = 0.0;
    downloadSpeed = 0.0;
    
    DWORD dwSize = 0;
    DWORD dwRetVal = GetIfTable(nullptr, &dwSize, FALSE);
    
    if (dwRetVal != ERROR_INSUFFICIENT_BUFFER) {
        return;
    }
    
    std::vector<BYTE> buffer(dwSize);
    PMIB_IFTABLE pIfTable = reinterpret_cast<PMIB_IFTABLE>(buffer.data());
    
    dwRetVal = GetIfTable(pIfTable, &dwSize, FALSE);
    if (dwRetVal != NO_ERROR) {
        return;
    }
    
    auto currentTime = std::chrono::steady_clock::now();
    
    if (!m_bFirstNetworkRead) {
        auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - m_lastNetworkTime).count();
        if (timeDiff > 0) {
            // 找到活动的网络接口
            for (DWORD i = 0; i < pIfTable->dwNumEntries; i++) {
                if (pIfTable->table[i].dwOperStatus == MIB_IF_OPER_STATUS_OPERATIONAL &&
                    pIfTable->table[i].dwType == MIB_IF_TYPE_ETHERNET) {
                    
                    DWORD currentInOctets = pIfTable->table[i].dwInOctets;
                    DWORD currentOutOctets = pIfTable->table[i].dwOutOctets;
                    
                    if (currentInOctets >= m_dwLastInOctets && currentOutOctets >= m_dwLastOutOctets) {
                        double inDiff = static_cast<double>(currentInOctets - m_dwLastInOctets);
                        double outDiff = static_cast<double>(currentOutOctets - m_dwLastOutOctets);
                        double timeInSeconds = timeDiff / 1000.0;
                        
                        downloadSpeed = (inDiff / timeInSeconds) / 1024.0;  // KB/s
                        uploadSpeed = (outDiff / timeInSeconds) / 1024.0;   // KB/s
                    }
                    
                    m_dwLastInOctets = currentInOctets;
                    m_dwLastOutOctets = currentOutOctets;
                    break;
                }
            }
        }
    } else {
        // 第一次读取，只记录数据
        for (DWORD i = 0; i < pIfTable->dwNumEntries; i++) {
            if (pIfTable->table[i].dwOperStatus == MIB_IF_OPER_STATUS_OPERATIONAL &&
                pIfTable->table[i].dwType == MIB_IF_TYPE_ETHERNET) {
                m_dwLastInOctets = pIfTable->table[i].dwInOctets;
                m_dwLastOutOctets = pIfTable->table[i].dwOutOctets;
                break;
            }
        }
        m_bFirstNetworkRead = false;
    }
    
    m_lastNetworkTime = currentTime;
}

std::string SystemInfo::GetCPUName() {
    return GetCPUNameFromRegistry();
}

std::string SystemInfo::GetCPUNameFromRegistry() {
    std::wstring cpuName;
    if (Utils::ReadRegistryString(HKEY_LOCAL_MACHINE, 
                                 L"HARDWARE\\DESCRIPTION\\System\\CentralProcessor\\0",
                                 L"ProcessorNameString", cpuName)) {
        return Utils::WStringToString(cpuName);
    }
    return "Unknown CPU";
}

std::string SystemInfo::GetGPUName() {
    // 简化实现，返回默认值
    return "Unknown GPU";
}

void SystemInfo::GetMemoryInfo(size_t& total, size_t& available) {
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    
    if (GlobalMemoryStatusEx(&memInfo)) {
        total = static_cast<size_t>(memInfo.ullTotalPhys / (1024 * 1024));      // MB
        available = static_cast<size_t>(memInfo.ullAvailPhys / (1024 * 1024));  // MB
    } else {
        total = 0;
        available = 0;
    }
}
