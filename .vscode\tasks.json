{"version": "2.0.0", "tasks": [{"label": "build-mingw", "type": "shell", "command": "g++", "args": ["-std=c++17", "-Wall", "-Wextra", "-Iin<PERSON><PERSON>", "-DUNICODE", "-D_UNICODE", "src/main.cpp", "src/SystemInfo.cpp", "src/MonitorWindow.cpp", "src/Utils.cpp", "-o", "bin/SystemMonitor.exe", "-luser32", "-lgdi32", "-lkernel32", "-ladvapi32", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lpsapi", "-lpdh", "-<PERSON><PERSON><PERSON><PERSON>", "-lws2_32", "-lshl<PERSON><PERSON>"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$gcc"], "detail": "使用MinGW g++编译器构建项目"}, {"label": "build-msvc", "type": "shell", "command": "cl", "args": ["/EHsc", "/std:c++17", "/I", "include", "/DUNICODE", "/D_UNICODE", "src\\main.cpp", "src\\SystemInfo.cpp", "src\\MonitorWindow.cpp", "src\\Utils.cpp", "/Fe:bin\\SystemMonitor.exe", "/link", "user32.lib", "gdi32.lib", "kernel32.lib", "advapi32.lib", "shell32.lib", "ole32.lib", "oleaut32.lib", "uuid.lib", "psapi.lib", "pdh.lib", "iphlpapi.lib", "ws2_32.lib", "shlwapi.lib", "wbemuuid.lib"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "detail": "使用MSVC编译器构建项目"}, {"label": "clean", "type": "shell", "command": "rm", "args": ["-rf", "bin", "obj"], "group": "build", "presentation": {"echo": true, "reveal": "silent"}, "detail": "清理构建文件"}, {"label": "run", "type": "shell", "command": "./bin/SystemMonitor.exe", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "dependsOn": "build-mingw", "detail": "构建并运行程序"}, {"type": "cppbuild", "label": "C/C++: g++.exe 生成活动文件", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}]}